@import "tailwindcss";

@font-face {
  font-family: "MyCustomFont";
  src: url("/fonts/montserrat/Montserrat.woff2") format("woff2");
  font-weight: normal;
  font-style: normal;
}

@theme {
  /* Orange Color */
  --color-orange: #ea6a12;
  --color-orange-light: #FDF8F5;
  --color-orange-border: #F2A36D;

  /* Green Color */
  --color-green: #8bc652;
  --color-active: #219342;
  --color-green-light: rgba(139, 198, 82, 0.2);

  /* Red Color */
  --color-red: #eb262b;
  --color-red-light: #fef2f2;
  
  /* Gray Color */
  --color-border: #E7E7E7;
}

body {
  margin: 0;
  font-family: 'MyCustomFont', sans-serif;
}

.custom-ag-table {
  .ag-header {
    @apply bg-green border-b-0 rounded-[11px] mb-2;
  }

  .ag-header-row .ag-header-row-column {
    .ag-header-cell {
      @apply text-white;
    }
  }
}

.ag-root-wrapper.ag-layout-normal {
  border: none;
}

/* Style the scrolling viewport */
.ag-theme-alpine .ag-viewport {
  background-color: transparent;
}

/* Style the center columns viewport */
.ag-theme-alpine .ag-center-cols-viewport {
}

/* AG Grid custom styles */
.custom-ag-table .ag-root-wrapper {
  border: none;
  border-radius: 8px;
  overflow: hidden;
  background-color: transparent;
}

.custom-ag-table .ag-header-cell {
  font-weight: 600;
  @apply text-white;
  text-transform: capitalize;
  background-color: transparent;
}

.custom-ag-table .ag-row {
  @apply border border-border bg-white max-h-[114px] hover:bg-orange-light cursor-pointer rounded-[11px] hover:border-[#EA6A1299];
  max-height: 114px;
}

.custom-ag-table .ag-cell {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: transparent;
}

.ag-theme-alpine .custom-row {
}

.ag-theme-alpine .custom-row .ag-cell {
  @apply bg-white p-3 rounded-md shadow-md; /* card-like style */
}

/* Override AG Grid's default background colors */
.custom-ag-table .ag-theme-alpine {
  background-color: transparent;
}

.custom-ag-table .ag-theme-alpine .ag-header,
.custom-ag-table .ag-theme-alpine .ag-row,
.custom-ag-table .ag-theme-alpine .ag-cell,
.custom-ag-table .ag-theme-alpine .ag-header-cell {
  background-color: transparent !important;
}

/* Apply custom font to AG Grid components */
.custom-ag-table .ag-root-wrapper,
.custom-ag-table .ag-header-cell,
.custom-ag-table .ag-cell,
.custom-ag-table .ag-row {
  font-family: 'MyCustomFont', sans-serif;
}

/* Ensure font is applied to all AG Grid text elements */
.ag-theme-alpine {
  font-family: 'MyCustomFont', sans-serif;
}
