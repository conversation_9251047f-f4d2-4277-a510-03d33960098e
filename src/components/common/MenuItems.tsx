import React from "react";
import type { ProductData } from "../../pages/dashboard/dashboardPage";
import SwiperSlider from "./SwiperSlider";

interface MenuItemsProps {
  productData: ProductData[];
}

const MenuItems: React.FC<MenuItemsProps> = ({ productData }) => {
  const SliderItems = productData.map((item) => {
    return (
      <li key={item?.id}>
        <div>
          <div>
            <img src={item?.photo} alt="item" />
          </div>
          <div>
            <span>{item?.productName}</span>
            <span>23 Items Available</span>
          </div>
        </div>
      </li>
    );
  });
  return (
    <div>
      <ul>
        <SwiperSlider
          slides={SliderItems}
          autoplay={true}
          loop={true}
          slidesPerView={1.7}
          breakpoints={{
            640: { slidesPerView: 1.8 },
            768: { slidesPerView: 3.5 },
            1024: { slidesPerView: 4.5 },
            1280: { slidesPerView: 5 },
          }}
        />
      </ul>
    </div>
  );
};

export default MenuItems;
